import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Upload, FileText, Copy, Download, CheckCircle, X } from 'lucide-react';
import '../styles/SummarizerPage.css';

const SummarizerPage: React.FC = () => {
  const [inputText, setInputText] = useState('');
  const [summaryText, setSummaryText] = useState('');
  const [showSummary, setShowSummary] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [copySuccess, setCopySuccess] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const summaryRef = useRef<HTMLDivElement>(null);

  const handleFileUpload = (file: File) => {
    if (file && (file.type === 'text/plain' || file.name.endsWith('.pdf') || file.name.endsWith('.docx') || file.name.endsWith('.txt'))) {
      setUploadedFile(file);
      // Simulate file reading
      const reader = new FileReader();
      reader.onload = (e) => {
        // For demo purposes, we'll just show the file name
        // In real implementation, you'd parse PDF/DOCX content
        setInputText(`File uploaded: ${file.name}\n\nThis is simulated content from the uploaded file. In a real implementation, the actual file content would be extracted and displayed here.`);
      };
      reader.readAsText(file);
    }
  };

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const file = e.dataTransfer.files[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleRemoveFile = () => {
    setUploadedFile(null);
    setInputText('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const scrollToSummary = () => {
    if (summaryRef.current) {
      // Calculate the position with some offset for better visual positioning
      const elementTop = summaryRef.current.offsetTop;
      const offset = 100; // Adjust this value to control how much space above the summary section
      
      window.scrollTo({
        top: elementTop - offset,
        behavior: 'smooth'
      });
    }
  };

  const handleSummarize = async () => {
  if (!inputText.trim()) return;

  setIsLoading(true);
  setShowSummary(false);
  setSummaryText('');

  try {
    let response;

    if (uploadedFile) {
      // File upload: use FormData
      const formData = new FormData();
      formData.append('file', uploadedFile);

      response = await fetch('http://69.62.123.195:3000/api/summarize', {
        method: 'POST',
        body: formData,
      });
    } else {
      // Text input
      response = await fetch('http://69.62.123.195:3000/api/summarize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: inputText }),
      });
    }

    if (!response.ok) throw new Error('Failed to summarize the document.');

    const data = await response.json();
    setSummaryText(data.summary || 'No summary returned.');
    setShowSummary(true);

    // Scroll into view
    setTimeout(scrollToSummary, 150);
  } catch (error) {
    console.error('Summarization error:', error);
    setSummaryText('An error occurred while summarizing. Please try again.');
    setShowSummary(true);
  } finally {
    setIsLoading(false);
  }
};


  const handleCopy = async () => {
    if (!summaryText.trim()) {
      console.warn('No summary text to copy');
      return;
    }

    try {
      // Check if clipboard API is available
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(summaryText);
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      } else {
        // Fallback for older browsers or non-secure contexts
        const textArea = document.createElement('textarea');
        textArea.value = summaryText;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
          const successful = document.execCommand('copy');
          if (successful) {
            setCopySuccess(true);
            setTimeout(() => setCopySuccess(false), 2000);
          } else {
            throw new Error('Copy command failed');
          }
        } catch (fallbackErr) {
          console.error('Fallback copy failed: ', fallbackErr);
          alert('Copy failed. Please manually select and copy the text.');
        } finally {
          document.body.removeChild(textArea);
        }
      }
    } catch (err) {
      console.error('Failed to copy text: ', err);
      // Show user-friendly error message
      alert('Copy failed. Please manually select and copy the text.');
    }
  };

  const handleDownloadPDF = () => {
    // Mock PDF download
    const element = document.createElement('a');
    const file = new Blob([summaryText], { type: 'text/plain' });
    element.href = URL.createObjectURL(file);
    element.download = 'document-summary.txt';
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  const isTextDisabled = uploadedFile !== null;
  const isUploadDisabled = inputText.trim() !== '' && !uploadedFile;

  return (
    <div className="summarizer-page">
      <header className="summarizer-header">
        <div className="header-content">
          <Link to="/" className="back-button">
            <ArrowLeft size={20} />
            Back to Home
          </Link>
          <h1>Document Summarizer</h1>
        </div>
      </header>

      <main className="summarizer-main">
        <div className="summarizer-container">
          <div className="input-section">
            <div className="text-input-section">
              <label htmlFor="document-text">Paste your document text here:</label>
              <textarea
                id="document-text"
                className={`document-input ${isTextDisabled ? 'disabled' : ''}`}
                value={uploadedFile ? inputText : inputText}
                onChange={(e) => !isTextDisabled && setInputText(e.target.value)}
                placeholder={isTextDisabled ? "Text input disabled - file uploaded" : "Paste your document text here..."}
                rows={12}
                disabled={isTextDisabled}
              />
            </div>

            <div className="divider-section">
              <div className="divider-line"></div>
              <span className="divider-text">OR</span>
              <div className="divider-line"></div>
            </div>

            <div className="upload-section">
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileInputChange}
                accept=".txt,.pdf,.docx"
                style={{ display: 'none' }}
                disabled={isUploadDisabled}
              />
              
              {!uploadedFile ? (
                <div 
                  className={`upload-area ${isDragOver ? 'drag-over' : ''} ${isUploadDisabled ? 'disabled' : ''}`}
                  onClick={() => !isUploadDisabled && fileInputRef.current?.click()}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  <Upload size={32} />
                  <p className="upload-text">
                    {isUploadDisabled 
                      ? "Upload disabled - text entered above" 
                      : "Click to upload or drag and drop"
                    }
                  </p>
                  <p className="upload-subtext">PDF, DOCX, TXT files supported</p>
                </div>
              ) : (
                <div className="uploaded-file">
                  <div className="file-info">
                    <FileText size={24} />
                    <div className="file-details">
                      <span className="file-name">{uploadedFile.name}</span>
                      <span className="file-size">
                        {(uploadedFile.size / 1024).toFixed(1)} KB
                      </span>
                    </div>
                  </div>
                  <button className="remove-file" onClick={handleRemoveFile}>
                    <X size={20} />
                  </button>
                </div>
              )}
            </div>

            <button 
              className={`summarize-button ${isLoading ? 'loading' : ''}`}
              onClick={handleSummarize}
              disabled={!inputText.trim() || isLoading}
            >
              {isLoading ? 'Summarizing...' : 'Summarize Document'}
            </button>
          </div>

          <div 
            ref={summaryRef}
            className={`summary-section ${showSummary ? 'show' : ''}`}
          >
            <h3>Document Summary</h3>
            <textarea
              className="summary-output"
              value={summaryText}
              onChange={(e) => setSummaryText(e.target.value)}
              rows={10}
            />
            <div className="summary-actions">
              <button
                className={`action-button copy-button ${copySuccess ? 'success' : ''}`}
                onClick={handleCopy}
                disabled={!summaryText.trim()}
                title={!summaryText.trim() ? 'No text to copy' : 'Copy summary to clipboard'}
              >
                {copySuccess ? <CheckCircle size={18} /> : <Copy size={18} />}
                {copySuccess ? 'Copied!' : 'Copy'}
              </button>
              <button
                className="action-button download-button"
                onClick={handleDownloadPDF}
                disabled={!summaryText.trim()}
                title={!summaryText.trim() ? 'No text to download' : 'Download summary as text file'}
              >
                <Download size={18} />
                Download as Text
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default SummarizerPage;