.landing-page {
  min-height: 100vh;
}

.cta-section {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 80px 0;
  text-align: center;
}

.cta-button-large {
  background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
  color: #2d3748;
  padding: 20px 40px;
  font-size: 18px;
  font-weight: 600;
  border: 2px solid transparent;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: none;
  letter-spacing: normal;
  box-shadow: 0 10px 30px rgba(253, 203, 110, 0.4);
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.cta-button-large:hover {
  background: linear-gradient(135deg, #fdcb6e, #ffeaa7);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(253, 203, 110, 0.4);
}

.cta-button-large:active {
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .cta-section {
    padding: 60px 0;
  }

  .cta-button-large {
    padding: 16px 32px;
    font-size: 16px;
  }
}