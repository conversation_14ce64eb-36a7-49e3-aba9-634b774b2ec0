.landing-page {
  min-height: 100vh;
}

.cta-section {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 80px 0;
  text-align: center;
}

.cta-button-large {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 40px;
  font-size: 18px;
  font-weight: 700;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.cta-button-large:hover {
  transform: translateY(-3px);
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.cta-button-large:active {
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .cta-section {
    padding: 60px 0;
  }

  .cta-button-large {
    padding: 16px 32px;
    font-size: 16px;
  }
}