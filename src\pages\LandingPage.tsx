import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';
import Hero from '../components/Hero';
import Features from '../components/Features';
import HowItWorks from '../components/HowItWorks';
import Footer from '../components/Footer';
import '../styles/LandingPage.css';

const LandingPage: React.FC = () => {
  const navigate = useNavigate();

  const scrollToFeatures = () => {
    const featuresSection = document.getElementById('features');
    if (featuresSection) {
      featuresSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const goToSummarizer = () => {
    navigate('/summarizer');
  };

  return (
    <div className="landing-page">
      <Hero onCtaClick={scrollToFeatures} onSummarizerClick={goToSummarizer} />
      <Features />
      <HowItWorks />
      <div className="cta-section">
        <button className="cta-button-large" onClick={goToSummarizer}>
          Go to Summarizer
          <ArrowRight size={20} />
        </button>
      </div>
      <Footer />
    </div>
  );
};

export default LandingPage;