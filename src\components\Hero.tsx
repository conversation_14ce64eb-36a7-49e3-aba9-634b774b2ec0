import React from 'react';
import { FileText, ChevronDown, ArrowRight } from 'lucide-react';
import '../styles/Hero.css';

interface HeroProps {
  onCtaClick: () => void;
  onSummarizerClick: () => void;
}

const Hero: React.FC<HeroProps> = ({ onCtaClick, onSummarizerClick }) => {
  return (
    <section className="hero">
      <div className="hero-background">
        <div className="hero-gradient"></div>
      </div>
      <div className="hero-content">
        <div className="hero-icon">
          <FileText size={48} />
        </div>
        <h1 className="hero-title">
          Transform Your Documents into 
          <span className="highlight"> Clear Summaries</span>
        </h1>
        <p className="hero-tagline">
          Harness the power of AI to instantly summarize lengthy documents, 
          extract key insights, and save hours of reading time.
        </p>
        <div className="hero-buttons">
          <button className="hero-cta" onClick={onCtaClick}>
            Discover How It Works
            <ChevronDown size={20} />
          </button>
          <button className="hero-cta-secondary" onClick={onSummarizerClick}>
            Go to Summarizer
            <ArrowRight size={20} />
          </button>
        </div>
      </div>
      <div className="scroll-indicator">
        <div className="scroll-dot"></div>
      </div>
    </section>
  );
};

export default Hero;