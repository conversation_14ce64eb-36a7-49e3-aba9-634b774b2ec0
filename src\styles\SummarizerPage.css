.summarizer-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.summarizer-header {
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.back-button:hover {
  color: #764ba2;
}

.summarizer-header h1 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.summarizer-main {
  padding: 40px 0;
}

.summarizer-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.input-section {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.text-input-section {
  margin-bottom: 30px;
}

.text-input-section label {
  display: block;
  margin-bottom: 12px;
  font-weight: 600;
  color: #2d3748;
  font-size: 16px;
}

.document-input {
  width: 100%;
  padding: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  line-height: 1.6;
  resize: vertical;
  transition: all 0.3s ease;
  font-family: inherit;
  background: white;
}

.document-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.document-input.disabled {
  background: #f7fafc;
  color: #a0aec0;
  cursor: not-allowed;
}

.document-input::placeholder {
  color: #a0aec0;
}

.divider-section {
  display: flex;
  align-items: center;
  margin: 30px 0;
  gap: 16px;
}

.divider-line {
  flex: 1;
  height: 1px;
  background: #e2e8f0;
}

.divider-text {
  color: #718096;
  font-weight: 500;
  font-size: 14px;
  padding: 0 8px;
}

.upload-section {
  margin-bottom: 30px;
}

.upload-area {
  border: 2px dashed #e2e8f0;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.upload-area:hover:not(.disabled) {
  border-color: #667eea;
  background: #f0f4ff;
}

.upload-area.drag-over {
  border-color: #667eea;
  background: #f0f4ff;
  transform: scale(1.02);
}

.upload-area.disabled {
  background: #f7fafc;
  border-color: #cbd5e0;
  cursor: not-allowed;
  opacity: 0.6;
}

.upload-area svg {
  color: #667eea;
  margin-bottom: 16px;
}

.upload-area.disabled svg {
  color: #a0aec0;
}

.upload-text {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.upload-area.disabled .upload-text {
  color: #a0aec0;
}

.upload-subtext {
  font-size: 14px;
  color: #718096;
}

.upload-area.disabled .upload-subtext {
  color: #a0aec0;
}

.uploaded-file {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: #f0f4ff;
  border: 2px solid #667eea;
  border-radius: 12px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-info svg {
  color: #667eea;
}

.file-details {
  display: flex;
  flex-direction: column;
}

.file-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 14px;
}

.file-size {
  font-size: 12px;
  color: #718096;
}

.remove-file {
  background: none;
  border: none;
  color: #e53e3e;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.remove-file:hover {
  background: rgba(229, 62, 62, 0.1);
}

.privacy-notice {
  color: #a0aec0;
  font-size: 14px;
  text-align: center;
  margin: 20px 0 16px 0;
  line-height: 1.5;
  font-style: italic;
}

.summarize-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 32px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.summarize-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.summarize-button:disabled {
  background: #a0aec0;
  cursor: not-allowed;
  transform: none;
}

.summarize-button.loading {
  background: #a0aec0;
}

.summarize-button.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.summary-section {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translateY(-30px);
  margin-top: 0;
}

.summary-section.show {
  opacity: 1;
  max-height: 1000px;
  transform: translateY(0);
  margin-top: 20px;
}

.summary-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  opacity: 0;
  animation: fadeInUp 0.6s ease-out 0.2s forwards;
}

.summary-output {
  width: 100%;
  padding: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  line-height: 1.6;
  resize: vertical;
  margin-bottom: 20px;
  font-family: inherit;
  background: #f8fafc;
  transition: all 0.3s ease;
  opacity: 0;
  animation: fadeInUp 0.6s ease-out 0.4s forwards;
}

.summary-output:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: white;
}

.summary-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  opacity: 0;
  animation: fadeInUp 0.6s ease-out 0.6s forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.action-button {
  padding: 12px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #2d3748;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.action-button:hover:not(:disabled) {
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  border-color: #e2e8f0;
  color: #a0aec0;
}

.copy-button.success {
  border-color: #48bb78 !important;
  color: #48bb78 !important;
  background: #f0fff4 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(72, 187, 120, 0.15);
}

.download-button:hover {
  border-color: #764ba2;
  color: #764ba2;
}

/* Smooth scroll behavior for the entire page */
html {
  scroll-behavior: smooth;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .summarizer-main {
    padding: 20px 0;
  }

  .input-section,
  .summary-section {
    padding: 24px;
    margin: 0 16px 20px;
  }

  .summarizer-container {
    padding: 0;
  }

  .summary-actions {
    flex-direction: column;
  }

  .action-button {
    justify-content: center;
  }

  .upload-area {
    padding: 30px 20px;
  }
}

@media (max-width: 480px) {
  .input-section,
  .summary-section {
    margin: 0 8px 16px;
    padding: 20px;
  }

  .document-input,
  .summary-output {
    font-size: 14px;
  }

  .summarize-button {
    font-size: 14px;
    padding: 14px 24px;
  }

  .upload-area {
    padding: 24px 16px;
  }
}
